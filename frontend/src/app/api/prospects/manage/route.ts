import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { z } from 'zod';

// Validation schema for prospect queries
const prospectQuerySchema = z.object({
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(20),
  status: z.enum(['active', 'converted', 'unsubscribed', 'bounced', 'spam_complaint', 'archived']).optional(),
  signupSource: z.enum(['website', 'landing_page', 'referral', 'social_media', 'advertisement', 'other']).optional(),
  emailVerified: z.coerce.boolean().optional(),
  practiceArea: z.enum(['personal_injury', 'criminal_defense', 'family_law']).optional(),
  search: z.string().optional(), // Search by email, name
  sortBy: z.enum(['created_at', 'updated_at', 'email', 'status']).default('created_at'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});

// Create Supabase client with user session
function createAuthenticatedClient(request: NextRequest) {
  const cookieStore = cookies();
  
  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_KEY!, // Use service role for admin operations
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
        set(name: string, value: string, options: any) {
          cookieStore.set({ name, value, ...options });
        },
        remove(name: string, options: any) {
          cookieStore.delete({ name, ...options });
        },
      },
    }
  );
}

// Check if user has admin privileges
async function checkAdminAccess(supabase: any): Promise<boolean> {
  try {
    const { data: { user }, error } = await supabase.auth.getUser();
    
    if (error || !user) {
      return false;
    }
    
    // Check if user is super admin or has marketing role
    const isSuperAdmin = user.user_metadata?.is_super_admin === true;
    const hasMarketingRole = user.app_metadata?.roles?.includes('marketing');
    
    return isSuperAdmin || hasMarketingRole;
  } catch (error) {
    console.error('Error checking admin access:', error);
    return false;
  }
}

// GET: List prospects with filtering and pagination
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    const { searchParams } = new URL(request.url);
    const queryParams = Object.fromEntries(searchParams.entries());
    const validatedQuery = prospectQuerySchema.parse(queryParams);
    
    const supabase = createAuthenticatedClient(request);
    
    // Check admin access
    const hasAccess = await checkAdminAccess(supabase);
    if (!hasAccess) {
      return NextResponse.json(
        { error: 'Unauthorized - Admin access required' },
        { status: 403 }
      );
    }
    
    // Build query
    let query = supabase
      .from('prospects')
      .select(`
        id,
        email,
        first_name,
        last_name,
        phone,
        signup_source,
        signup_page,
        utm_source,
        utm_medium,
        utm_campaign,
        practice_area_interest,
        case_urgency,
        estimated_case_value,
        status,
        email_verified,
        newsletter_subscribed,
        marketing_consent,
        gdpr_consent,
        converted_to_user_id,
        converted_to_tenant_id,
        converted_at,
        created_at,
        updated_at,
        last_contacted_at
      `, { count: 'exact' })
      .is('deleted_at', null);
    
    // Apply filters
    if (validatedQuery.status) {
      query = query.eq('status', validatedQuery.status);
    }
    
    if (validatedQuery.signupSource) {
      query = query.eq('signup_source', validatedQuery.signupSource);
    }
    
    if (validatedQuery.emailVerified !== undefined) {
      query = query.eq('email_verified', validatedQuery.emailVerified);
    }
    
    if (validatedQuery.practiceArea) {
      query = query.contains('practice_area_interest', [validatedQuery.practiceArea]);
    }
    
    if (validatedQuery.search) {
      const searchTerm = `%${validatedQuery.search}%`;
      query = query.or(`email.ilike.${searchTerm},first_name.ilike.${searchTerm},last_name.ilike.${searchTerm}`);
    }
    
    // Apply sorting
    query = query.order(validatedQuery.sortBy, { ascending: validatedQuery.sortOrder === 'asc' });
    
    // Apply pagination
    const offset = (validatedQuery.page - 1) * validatedQuery.limit;
    query = query.range(offset, offset + validatedQuery.limit - 1);
    
    const { data: prospects, error, count } = await query;
    
    if (error) {
      console.error('Error fetching prospects:', error);
      return NextResponse.json(
        { error: 'Failed to fetch prospects' },
        { status: 500 }
      );
    }
    
    // Calculate pagination info
    const totalPages = Math.ceil((count || 0) / validatedQuery.limit);
    
    return NextResponse.json({
      prospects: prospects || [],
      pagination: {
        page: validatedQuery.page,
        limit: validatedQuery.limit,
        total: count || 0,
        totalPages,
        hasNext: validatedQuery.page < totalPages,
        hasPrev: validatedQuery.page > 1,
      },
    });
    
  } catch (error) {
    console.error('Prospects list error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid query parameters', details: error.errors },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PATCH: Update prospect status or preferences
export async function PATCH(request: NextRequest): Promise<NextResponse> {
  try {
    const body = await request.json();
    const { prospectId, updates } = body;
    
    if (!prospectId) {
      return NextResponse.json(
        { error: 'Prospect ID is required' },
        { status: 400 }
      );
    }
    
    const supabase = createAuthenticatedClient(request);
    
    // Check admin access
    const hasAccess = await checkAdminAccess(supabase);
    if (!hasAccess) {
      return NextResponse.json(
        { error: 'Unauthorized - Admin access required' },
        { status: 403 }
      );
    }
    
    // Validate updates
    const allowedUpdates = [
      'status',
      'newsletter_subscribed',
      'marketing_consent',
      'communication_preferences',
      'practice_area_interest',
      'case_urgency',
      'estimated_case_value',
    ];
    
    const filteredUpdates = Object.keys(updates)
      .filter(key => allowedUpdates.includes(key))
      .reduce((obj, key) => {
        obj[key] = updates[key];
        return obj;
      }, {} as any);
    
    if (Object.keys(filteredUpdates).length === 0) {
      return NextResponse.json(
        { error: 'No valid updates provided' },
        { status: 400 }
      );
    }
    
    // Add updated_at timestamp
    filteredUpdates.updated_at = new Date().toISOString();
    
    const { data: updatedProspect, error } = await supabase
      .from('prospects')
      .update(filteredUpdates)
      .eq('id', prospectId)
      .select()
      .single();
    
    if (error) {
      console.error('Error updating prospect:', error);
      return NextResponse.json(
        { error: 'Failed to update prospect' },
        { status: 500 }
      );
    }
    
    // Log the update interaction
    await supabase
      .from('prospect_interactions')
      .insert({
        prospect_id: prospectId,
        interaction_type: 'admin_update',
        subject: 'Prospect updated by admin',
        metadata: {
          updated_fields: Object.keys(filteredUpdates),
          updates: filteredUpdates,
        },
      });
    
    return NextResponse.json({
      success: true,
      prospect: updatedProspect,
    });
    
  } catch (error) {
    console.error('Prospect update error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE: Soft delete prospect (GDPR compliance)
export async function DELETE(request: NextRequest): Promise<NextResponse> {
  try {
    const { searchParams } = new URL(request.url);
    const prospectId = searchParams.get('id');
    
    if (!prospectId) {
      return NextResponse.json(
        { error: 'Prospect ID is required' },
        { status: 400 }
      );
    }
    
    const supabase = createAuthenticatedClient(request);
    
    // Check admin access
    const hasAccess = await checkAdminAccess(supabase);
    if (!hasAccess) {
      return NextResponse.json(
        { error: 'Unauthorized - Admin access required' },
        { status: 403 }
      );
    }
    
    // Soft delete the prospect
    const { error } = await supabase
      .from('prospects')
      .update({
        status: 'archived',
        deleted_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .eq('id', prospectId);
    
    if (error) {
      console.error('Error deleting prospect:', error);
      return NextResponse.json(
        { error: 'Failed to delete prospect' },
        { status: 500 }
      );
    }
    
    // Log the deletion
    await supabase
      .from('prospect_interactions')
      .insert({
        prospect_id: prospectId,
        interaction_type: 'admin_update',
        subject: 'Prospect deleted by admin',
        metadata: {
          action: 'soft_delete',
          deleted_at: new Date().toISOString(),
        },
      });
    
    return NextResponse.json({
      success: true,
      message: 'Prospect deleted successfully',
    });
    
  } catch (error) {
    console.error('Prospect deletion error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
