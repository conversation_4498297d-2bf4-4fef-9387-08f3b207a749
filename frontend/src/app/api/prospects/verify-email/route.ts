import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { z } from 'zod';

// Validation schemas
const sendVerificationSchema = z.object({
  email: z.string().email({ message: 'Invalid email address' }),
});

const verifyEmailSchema = z.object({
  token: z.string().min(1, { message: 'Verification token is required' }),
});

// Create Supabase client with service role
function createServiceClient() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_KEY;
  
  if (!supabaseUrl || !supabaseServiceKey) {
    throw new Error('Missing Supabase configuration');
  }
  
  return createClient(supabaseUrl, supabaseServiceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  });
}

// Send verification email (would integrate with your email service)
async function sendVerificationEmail(email: string, token: string): Promise<boolean> {
  // TODO: Integrate with your email service (SendGrid, Resend, etc.)
  // For now, just log the verification link
  const verificationUrl = `${process.env.NEXT_PUBLIC_SITE_URL}/verify-email?token=${token}`;
  
  console.log(`Verification email for ${email}: ${verificationUrl}`);
  
  // In production, you would send an actual email here
  // Example with a hypothetical email service:
  /*
  try {
    await emailService.send({
      to: email,
      subject: 'Verify your email address',
      template: 'email-verification',
      data: {
        verificationUrl,
        email,
      }
    });
    return true;
  } catch (error) {
    console.error('Failed to send verification email:', error);
    return false;
  }
  */
  
  return true; // Return true for development
}

// POST: Send email verification
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    const body = await request.json();
    const validatedData = sendVerificationSchema.parse(body);
    
    const supabase = createServiceClient();
    
    // Check if prospect exists and is not already verified
    const { data: prospect, error: checkError } = await supabase
      .from('prospects')
      .select('id, email_verified, status')
      .eq('email', validatedData.email)
      .eq('status', 'active')
      .is('deleted_at', null)
      .maybeSingle();
    
    if (checkError) {
      console.error('Error checking prospect:', checkError);
      return NextResponse.json(
        { error: 'Database error' },
        { status: 500 }
      );
    }
    
    if (!prospect) {
      return NextResponse.json(
        { error: 'Email not found in our records' },
        { status: 404 }
      );
    }
    
    if (prospect.email_verified) {
      return NextResponse.json(
        { error: 'Email is already verified' },
        { status: 400 }
      );
    }
    
    // Generate verification token using the database function
    const { data: tokenResult, error: tokenError } = await supabase
      .rpc('generate_email_verification_token', {
        prospect_email: validatedData.email
      });
    
    if (tokenError || !tokenResult) {
      console.error('Error generating verification token:', tokenError);
      return NextResponse.json(
        { error: 'Failed to generate verification token' },
        { status: 500 }
      );
    }
    
    // Send verification email
    const emailSent = await sendVerificationEmail(validatedData.email, tokenResult);
    
    if (!emailSent) {
      return NextResponse.json(
        { error: 'Failed to send verification email' },
        { status: 500 }
      );
    }
    
    // Log the interaction
    await supabase
      .from('prospect_interactions')
      .insert({
        prospect_id: prospect.id,
        interaction_type: 'email_sent',
        subject: 'Email verification sent',
        metadata: {
          email_type: 'verification',
          sent_at: new Date().toISOString(),
        },
      });
    
    return NextResponse.json({
      success: true,
      message: 'Verification email sent successfully',
    });
    
  } catch (error) {
    console.error('Send verification error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid data', details: error.errors },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT: Verify email with token
export async function PUT(request: NextRequest): Promise<NextResponse> {
  try {
    const body = await request.json();
    const validatedData = verifyEmailSchema.parse(body);
    
    const supabase = createServiceClient();
    
    // Verify email using the database function
    const { data: verificationResult, error: verifyError } = await supabase
      .rpc('verify_prospect_email', {
        verification_token: validatedData.token
      });
    
    if (verifyError) {
      console.error('Error verifying email:', verifyError);
      return NextResponse.json(
        { error: 'Database error during verification' },
        { status: 500 }
      );
    }
    
    if (!verificationResult) {
      return NextResponse.json(
        { error: 'Invalid or expired verification token' },
        { status: 400 }
      );
    }
    
    // Get the verified prospect details
    const { data: prospect, error: prospectError } = await supabase
      .from('prospects')
      .select('id, email, first_name, last_name')
      .eq('email_verified', true)
      .order('email_verified_at', { ascending: false })
      .limit(1)
      .maybeSingle();
    
    if (prospectError || !prospect) {
      console.error('Error fetching verified prospect:', prospectError);
      return NextResponse.json(
        { error: 'Verification successful but failed to fetch prospect details' },
        { status: 500 }
      );
    }
    
    return NextResponse.json({
      success: true,
      message: 'Email verified successfully',
      prospect: {
        id: prospect.id,
        email: prospect.email,
        firstName: prospect.first_name,
        lastName: prospect.last_name,
      },
    });
    
  } catch (error) {
    console.error('Email verification error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid data', details: error.errors },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// GET: Check verification status
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    const { searchParams } = new URL(request.url);
    const email = searchParams.get('email');
    
    if (!email) {
      return NextResponse.json(
        { error: 'Email parameter is required' },
        { status: 400 }
      );
    }
    
    const supabase = createServiceClient();
    
    const { data: prospect, error } = await supabase
      .from('prospects')
      .select('email_verified, email_verification_sent_at')
      .eq('email', email)
      .eq('status', 'active')
      .is('deleted_at', null)
      .maybeSingle();
    
    if (error) {
      console.error('Error checking verification status:', error);
      return NextResponse.json(
        { error: 'Database error' },
        { status: 500 }
      );
    }
    
    if (!prospect) {
      return NextResponse.json(
        { error: 'Email not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      emailVerified: prospect.email_verified,
      verificationSentAt: prospect.email_verification_sent_at,
    });
    
  } catch (error) {
    console.error('Check verification status error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
