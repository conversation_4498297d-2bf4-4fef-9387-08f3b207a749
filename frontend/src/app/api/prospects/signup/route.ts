import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { z } from 'zod';
import { headers } from 'next/headers';

// Validation schema for prospect signup
const prospectSignupSchema = z.object({
  email: z.string().email({ message: 'Invalid email address' }),
  firstName: z.string().min(1, { message: 'First name is required' }).optional(),
  lastName: z.string().min(1, { message: 'Last name is required' }).optional(),
  phone: z.string().optional(),
  
  // Signup context
  signupSource: z.enum(['website', 'landing_page', 'referral', 'social_media', 'advertisement', 'other']).default('website'),
  signupPage: z.string().optional(),
  utmSource: z.string().optional(),
  utmMedium: z.string().optional(),
  utmCampaign: z.string().optional(),
  utmContent: z.string().optional(),
  utmTerm: z.string().optional(),
  
  // Preferences
  newsletterSubscribed: z.boolean().default(true),
  marketingConsent: z.boolean().default(false),
  communicationPreferences: z.object({
    email: z.boolean().default(true),
    sms: z.boolean().default(false),
    phone: z.boolean().default(false),
  }).default({ email: true, sms: false, phone: false }),
  
  // Legal interest
  practiceAreaInterest: z.array(z.enum(['personal_injury', 'criminal_defense', 'family_law'])).default([]),
  caseUrgency: z.enum(['immediate', 'within_month', 'within_quarter', 'planning_ahead']).optional(),
  estimatedCaseValue: z.enum(['under_10k', '10k_50k', '50k_100k', 'over_100k', 'unknown']).optional(),
  
  // GDPR compliance
  gdprConsent: z.boolean(),
  
  // Turnstile token for bot protection
  turnstileToken: z.string().optional(),
});

type ProspectSignupData = z.infer<typeof prospectSignupSchema>;

// Create Supabase client with service role for database operations
function createServiceClient() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_KEY; // Service role key
  
  if (!supabaseUrl || !supabaseServiceKey) {
    throw new Error('Missing Supabase configuration');
  }
  
  return createClient(supabaseUrl, supabaseServiceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  });
}

// Verify Turnstile token
async function verifyTurnstileToken(token: string, ip: string): Promise<boolean> {
  if (!process.env.TURNSTILE_SECRET_KEY) {
    console.warn('Turnstile secret key not configured, skipping verification');
    return true; // Allow in development
  }
  
  try {
    const formData = new URLSearchParams();
    formData.append('secret', process.env.TURNSTILE_SECRET_KEY);
    formData.append('response', token);
    formData.append('remoteip', ip);
    
    const result = await fetch('https://challenges.cloudflare.com/turnstile/v0/siteverify', {
      method: 'POST',
      body: formData,
    });
    
    const data = await result.json();
    return data.success === true;
  } catch (error) {
    console.error('Turnstile verification error:', error);
    return false;
  }
}

// Get client IP address
function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');
  
  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }
  
  if (realIP) {
    return realIP;
  }
  
  return request.ip || 'unknown';
}

// POST: Create a new prospect signup
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    const body = await request.json();
    const validatedData = prospectSignupSchema.parse(body);
    
    // Get client information
    const ip = getClientIP(request);
    const userAgent = request.headers.get('user-agent') || 'unknown';
    const referrer = request.headers.get('referer') || null;
    
    // Verify Turnstile token if provided
    if (validatedData.turnstileToken) {
      const isValidToken = await verifyTurnstileToken(validatedData.turnstileToken, ip);
      if (!isValidToken) {
        return NextResponse.json(
          { error: 'Invalid captcha token' },
          { status: 400 }
        );
      }
    }
    
    // GDPR consent is required
    if (!validatedData.gdprConsent) {
      return NextResponse.json(
        { error: 'GDPR consent is required' },
        { status: 400 }
      );
    }
    
    const supabase = createServiceClient();
    
    // Check if prospect already exists
    const { data: existingProspect, error: checkError } = await supabase
      .from('prospects')
      .select('id, status, email_verified')
      .eq('email', validatedData.email)
      .maybeSingle();
    
    if (checkError) {
      console.error('Error checking existing prospect:', checkError);
      return NextResponse.json(
        { error: 'Database error' },
        { status: 500 }
      );
    }
    
    // If prospect exists and is active, update their preferences
    if (existingProspect && existingProspect.status === 'active') {
      const { error: updateError } = await supabase
        .from('prospects')
        .update({
          first_name: validatedData.firstName,
          last_name: validatedData.lastName,
          phone: validatedData.phone,
          newsletter_subscribed: validatedData.newsletterSubscribed,
          marketing_consent: validatedData.marketingConsent,
          communication_preferences: validatedData.communicationPreferences,
          practice_area_interest: validatedData.practiceAreaInterest,
          case_urgency: validatedData.caseUrgency,
          estimated_case_value: validatedData.estimatedCaseValue,
          updated_at: new Date().toISOString(),
        })
        .eq('email', validatedData.email);
      
      if (updateError) {
        console.error('Error updating prospect:', updateError);
        return NextResponse.json(
          { error: 'Failed to update signup' },
          { status: 500 }
        );
      }
      
      return NextResponse.json({
        success: true,
        message: 'Preferences updated successfully',
        prospectId: existingProspect.id,
        emailVerified: existingProspect.email_verified,
      });
    }
    
    // Create new prospect
    const prospectData = {
      email: validatedData.email,
      first_name: validatedData.firstName,
      last_name: validatedData.lastName,
      phone: validatedData.phone,
      signup_source: validatedData.signupSource,
      signup_page: validatedData.signupPage,
      utm_source: validatedData.utmSource,
      utm_medium: validatedData.utmMedium,
      utm_campaign: validatedData.utmCampaign,
      utm_content: validatedData.utmContent,
      utm_term: validatedData.utmTerm,
      newsletter_subscribed: validatedData.newsletterSubscribed,
      marketing_consent: validatedData.marketingConsent,
      communication_preferences: validatedData.communicationPreferences,
      practice_area_interest: validatedData.practiceAreaInterest,
      case_urgency: validatedData.caseUrgency,
      estimated_case_value: validatedData.estimatedCaseValue,
      gdpr_consent: validatedData.gdprConsent,
      gdpr_consent_date: new Date().toISOString(),
      gdpr_consent_ip: ip,
      gdpr_consent_user_agent: userAgent,
      data_retention_until: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 1 year
      ip_address: ip,
      user_agent: userAgent,
      referrer_url: referrer,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    };
    
    const { data: newProspect, error: insertError } = await supabase
      .from('prospects')
      .insert(prospectData)
      .select('id, email')
      .single();
    
    if (insertError) {
      console.error('Error creating prospect:', insertError);
      
      // Handle duplicate email error
      if (insertError.code === '23505') {
        return NextResponse.json(
          { error: 'Email already registered' },
          { status: 409 }
        );
      }
      
      return NextResponse.json(
        { error: 'Failed to create signup' },
        { status: 500 }
      );
    }
    
    // Log the signup interaction
    await supabase
      .from('prospect_interactions')
      .insert({
        prospect_id: newProspect.id,
        interaction_type: 'newsletter_signup',
        subject: 'Initial signup',
        metadata: {
          signup_source: validatedData.signupSource,
          practice_areas: validatedData.practiceAreaInterest,
          utm_data: {
            source: validatedData.utmSource,
            medium: validatedData.utmMedium,
            campaign: validatedData.utmCampaign,
          }
        },
        ip_address: ip,
        user_agent: userAgent,
        referrer_url: referrer,
      });
    
    return NextResponse.json({
      success: true,
      message: 'Signup successful',
      prospectId: newProspect.id,
      emailVerified: false,
    });
    
  } catch (error) {
    console.error('Prospect signup error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid data', details: error.errors },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
